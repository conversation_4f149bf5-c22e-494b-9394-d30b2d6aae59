import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from PIL import Image, ImageTk
import cv2
import numpy as np
import tensorflow as tf
from tensorflow import keras
import os

class ColonCancerDetectorUI:
    def __init__(self, root):
        self.root = root
        self.root.title("结肠癌细胞检测器")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f0f0')
        
        # 模型相关
        self.model = None
        self.img_size = (224, 224)
        self.class_names = ['癌细胞', '良性细胞']
        
        # 当前图像
        self.current_image = None
        self.current_image_path = None
        
        self.setup_ui()
        self.load_model()
    
    def setup_ui(self):
        """设置用户界面"""
        # 标题
        title_label = tk.Label(
            self.root, 
            text="结肠癌细胞检测器", 
            font=("Arial", 20, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        title_label.pack(pady=20)
        
        # 按钮框架
        button_frame = tk.Frame(self.root, bg='#f0f0f0')
        button_frame.pack(pady=10)
        
        # 上传图片按钮
        self.upload_btn = tk.Button(
            button_frame,
            text="上传图片",
            command=self.upload_image,
            font=("Arial", 12),
            bg='#3498db',
            fg='white',
            padx=20,
            pady=10,
            cursor='hand2'
        )
        self.upload_btn.pack(side=tk.LEFT, padx=10)
        
        # 检测按钮
        self.detect_btn = tk.Button(
            button_frame,
            text="开始检测",
            command=self.detect_cancer,
            font=("Arial", 12),
            bg='#e74c3c',
            fg='white',
            padx=20,
            pady=10,
            cursor='hand2',
            state='disabled'
        )
        self.detect_btn.pack(side=tk.LEFT, padx=10)
        
        # 清除按钮
        self.clear_btn = tk.Button(
            button_frame,
            text="清除",
            command=self.clear_results,
            font=("Arial", 12),
            bg='#95a5a6',
            fg='white',
            padx=20,
            pady=10,
            cursor='hand2'
        )
        self.clear_btn.pack(side=tk.LEFT, padx=10)
        
        # 图像显示框架
        image_frame = tk.Frame(self.root, bg='#f0f0f0')
        image_frame.pack(pady=20, expand=True, fill='both')
        
        # 图像显示标签
        self.image_label = tk.Label(
            image_frame,
            text="请上传一张结肠细胞图片",
            font=("Arial", 14),
            bg='white',
            fg='#7f8c8d',
            width=50,
            height=15,
            relief='sunken',
            bd=2
        )
        self.image_label.pack(pady=10)
        
        # 结果显示框架
        result_frame = tk.Frame(self.root, bg='#f0f0f0')
        result_frame.pack(pady=10, fill='x', padx=50)
        
        # 结果标签
        self.result_label = tk.Label(
            result_frame,
            text="",
            font=("Arial", 16, "bold"),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        self.result_label.pack()
        
        # 置信度标签
        self.confidence_label = tk.Label(
            result_frame,
            text="",
            font=("Arial", 12),
            bg='#f0f0f0',
            fg='#7f8c8d'
        )
        self.confidence_label.pack()
        
        # 进度条
        self.progress = ttk.Progressbar(
            self.root,
            mode='indeterminate',
            length=300
        )
        
        # 状态标签
        self.status_label = tk.Label(
            self.root,
            text="就绪",
            font=("Arial", 10),
            bg='#f0f0f0',
            fg='#27ae60'
        )
        self.status_label.pack(side=tk.BOTTOM, pady=5)
    
    def load_model(self):
        """加载训练好的模型"""
        try:
            model_path = 'colon_cancer_classifier.h5'
            if os.path.exists(model_path):
                self.model = keras.models.load_model(model_path)
                self.status_label.config(text="模型加载成功", fg='#27ae60')
            else:
                # 尝试加载备用模型
                backup_path = 'best_colon_cancer_model.h5'
                if os.path.exists(backup_path):
                    self.model = keras.models.load_model(backup_path)
                    self.status_label.config(text="备用模型加载成功", fg='#27ae60')
                else:
                    self.status_label.config(text="未找到模型文件，请先训练模型", fg='#e74c3c')
                    messagebox.showwarning(
                        "警告", 
                        "未找到训练好的模型文件！\n请先运行 colon_cancer_classifier.py 训练模型。"
                    )
        except Exception as e:
            self.status_label.config(text=f"模型加载失败: {str(e)}", fg='#e74c3c')
            messagebox.showerror("错误", f"模型加载失败: {str(e)}")
    
    def upload_image(self):
        """上传图片"""
        file_types = [
            ("图片文件", "*.jpg *.jpeg *.png *.bmp *.tiff"),
            ("JPEG文件", "*.jpg *.jpeg"),
            ("PNG文件", "*.png"),
            ("所有文件", "*.*")
        ]
        
        file_path = filedialog.askopenfilename(
            title="选择图片文件",
            filetypes=file_types
        )
        
        if file_path:
            try:
                # 加载并显示图片
                self.current_image_path = file_path
                self.display_image(file_path)
                self.detect_btn.config(state='normal')
                self.status_label.config(text="图片上传成功", fg='#27ae60')
                
                # 清除之前的结果
                self.result_label.config(text="")
                self.confidence_label.config(text="")
                
            except Exception as e:
                messagebox.showerror("错误", f"无法加载图片: {str(e)}")
                self.status_label.config(text="图片加载失败", fg='#e74c3c')
    
    def display_image(self, image_path):
        """显示图片"""
        try:
            # 加载图片
            image = Image.open(image_path)
            
            # 调整图片大小以适应显示
            display_size = (400, 300)
            image.thumbnail(display_size, Image.Resampling.LANCZOS)
            
            # 转换为PhotoImage
            photo = ImageTk.PhotoImage(image)
            
            # 更新标签
            self.image_label.config(image=photo, text="")
            self.image_label.image = photo  # 保持引用
            
        except Exception as e:
            messagebox.showerror("错误", f"无法显示图片: {str(e)}")
    
    def detect_cancer(self):
        """检测癌细胞"""
        if self.model is None:
            messagebox.showerror("错误", "模型未加载，无法进行检测")
            return
        
        if self.current_image_path is None:
            messagebox.showerror("错误", "请先上传图片")
            return
        
        try:
            # 显示进度条
            self.progress.pack(pady=10)
            self.progress.start()
            self.status_label.config(text="正在检测...", fg='#f39c12')
            self.root.update()
            
            # 预处理图像
            img = cv2.imread(self.current_image_path)
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            img = cv2.resize(img, self.img_size)
            img = img.astype('float32') / 255.0
            img = np.expand_dims(img, axis=0)
            
            # 进行预测
            prediction = self.model.predict(img, verbose=0)
            predicted_class = np.argmax(prediction, axis=1)[0]
            confidence = np.max(prediction)
            
            # 停止进度条
            self.progress.stop()
            self.progress.pack_forget()
            
            # 显示结果
            class_name = self.class_names[predicted_class]
            
            # 设置结果颜色
            if predicted_class == 0:  # 癌细胞
                result_color = '#e74c3c'
                status_text = "检测到癌细胞"
            else:  # 良性
                result_color = '#27ae60'
                status_text = "检测到良性细胞"
            
            self.result_label.config(
                text=f"检测结果: {class_name}",
                fg=result_color
            )
            
            self.confidence_label.config(
                text=f"置信度: {confidence:.2%}"
            )
            
            self.status_label.config(text=status_text, fg=result_color)
            
            # 显示详细结果对话框
            detail_msg = f"检测结果: {class_name}\n置信度: {confidence:.2%}\n\n"
            if predicted_class == 0:
                detail_msg += "⚠️ 检测到癌细胞特征，建议进一步医学检查"
            else:
                detail_msg += "✅ 检测到良性细胞特征"
            
            messagebox.showinfo("检测结果", detail_msg)
            
        except Exception as e:
            # 停止进度条
            self.progress.stop()
            self.progress.pack_forget()
            
            messagebox.showerror("错误", f"检测失败: {str(e)}")
            self.status_label.config(text="检测失败", fg='#e74c3c')
    
    def clear_results(self):
        """清除结果"""
        self.current_image_path = None
        self.image_label.config(
            image="",
            text="请上传一张结肠细胞图片"
        )
        self.image_label.image = None
        
        self.result_label.config(text="")
        self.confidence_label.config(text="")
        self.detect_btn.config(state='disabled')
        self.status_label.config(text="就绪", fg='#27ae60')


def main():
    """主函数"""
    root = tk.Tk()
    app = ColonCancerDetectorUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
