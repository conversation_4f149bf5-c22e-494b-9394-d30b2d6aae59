import os
import random
import numpy as np
import matplotlib.pyplot as plt
from colon_cancer_classifier import ColonCancerClassifier
import cv2
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
import seaborn as sns

def test_on_test_set():
    """
    在保存的测试集上测试模型
    """
    print("=== 在测试集上评估模型 ===")
    
    # 创建分类器实例
    classifier = ColonCancerClassifier()
    
    # 尝试加载已训练的模型
    if not classifier.load_model('colon_cancer_classifier.h5'):
        if not classifier.load_model('best_colon_cancer_model.h5'):
            print("未找到训练好的模型，请先运行训练脚本")
            return
    
    # 加载数据划分
    if not classifier.load_data_split():
        print("未找到数据划分文件，请先运行训练脚本")
        return
    
    # 在测试集上预测
    print("正在预测测试集...")
    test_predictions = classifier.model.predict(classifier.X_test)
    test_pred_classes = np.argmax(test_predictions, axis=1)
    
    # 计算准确率
    test_accuracy = accuracy_score(classifier.y_test, test_pred_classes)
    print(f"测试集准确率: {test_accuracy:.4f}")
    
    # 分类报告
    print("\n分类报告:")
    print(classification_report(classifier.y_test, test_pred_classes, 
                              target_names=['癌细胞', '良性细胞']))
    
    # 混淆矩阵
    cm = confusion_matrix(classifier.y_test, test_pred_classes)
    
    # 绘制混淆矩阵
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
               xticklabels=['癌细胞', '良性细胞'],
               yticklabels=['癌细胞', '良性细胞'])
    plt.title('测试集混淆矩阵')
    plt.ylabel('真实标签')
    plt.xlabel('预测标签')
    plt.tight_layout()
    plt.savefig('test_confusion_matrix.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return test_accuracy, cm

def test_random_samples_from_test_set():
    """
    从测试集中随机选择样本进行可视化测试
    """
    print("=== 测试集随机样本可视化 ===")
    
    # 创建分类器实例
    classifier = ColonCancerClassifier()
    
    # 尝试加载已训练的模型
    if not classifier.load_model('colon_cancer_classifier.h5'):
        if not classifier.load_model('best_colon_cancer_model.h5'):
            print("未找到训练好的模型，请先运行训练脚本")
            return
    
    # 加载数据划分
    if not classifier.load_data_split():
        print("未找到数据划分文件，请先运行训练脚本")
        return
    
    # 随机选择10个测试样本
    num_samples = min(10, len(classifier.X_test))
    random_indices = random.sample(range(len(classifier.X_test)), num_samples)
    
    # 预测选中的样本
    test_predictions = classifier.model.predict(classifier.X_test[random_indices])
    test_pred_classes = np.argmax(test_predictions, axis=1)
    
    # 创建图像网格显示结果
    fig, axes = plt.subplots(2, 5, figsize=(20, 8))
    axes = axes.flatten()
    
    correct_predictions = 0
    
    for i, idx in enumerate(random_indices):
        # 获取真实标签和预测结果
        true_class = classifier.y_test[idx]
        pred_class = test_pred_classes[i]
        confidence = np.max(test_predictions[i])
        
        true_label = '癌细胞' if true_class == 0 else '良性细胞'
        pred_label = '癌细胞' if pred_class == 0 else '良性细胞'
        
        # 检查预测是否正确
        is_correct = pred_class == true_class
        if is_correct:
            correct_predictions += 1
        
        # 显示图像
        img = classifier.X_test[idx]
        axes[i].imshow(img)
        
        # 设置标题颜色
        title_color = 'green' if is_correct else 'red'
        axes[i].set_title(
            f'真实: {true_label}\n预测: {pred_label}\n置信度: {confidence:.3f}',
            color=title_color,
            fontsize=10
        )
        axes[i].axis('off')
        
        print(f"样本 {i+1}: 真实={true_label}, 预测={pred_label}, "
              f"置信度={confidence:.3f}, 正确={is_correct}")
    
    # 计算这批样本的准确率
    accuracy = correct_predictions / num_samples
    print(f"\n随机样本测试结果:")
    print(f"总样本数: {num_samples}")
    print(f"正确预测: {correct_predictions}")
    print(f"准确率: {accuracy:.2%}")
    
    plt.suptitle(f'测试集随机样本 - 准确率: {accuracy:.2%}', fontsize=16)
    plt.tight_layout()
    plt.savefig('test_random_samples.png', dpi=300, bbox_inches='tight')
    plt.show()

def test_specific_image(image_path):
    """
    测试特定图像
    """
    print(f"=== 测试特定图像: {image_path} ===")
    
    if not os.path.exists(image_path):
        print(f"图像文件不存在: {image_path}")
        return
    
    # 创建分类器实例
    classifier = ColonCancerClassifier()
    
    # 尝试加载已训练的模型
    if not classifier.load_model('colon_cancer_classifier.h5'):
        if not classifier.load_model('best_colon_cancer_model.h5'):
            print("未找到训练好的模型，请先运行训练脚本")
            return
    
    # 预测
    predicted_class, confidence, predicted_label = classifier.predict_single_image(image_path)
    
    if predicted_class is not None:
        print(f"预测结果: {predicted_label}")
        print(f"置信度: {confidence:.4f}")
        
        # 给出建议
        if predicted_class == 0:  # 癌细胞
            print("⚠️ 检测到癌细胞特征，建议进一步医学检查")
        else:  # 良性
            print("✅ 检测到良性细胞特征")
    else:
        print("预测失败")

def show_data_distribution():
    """
    显示数据分布
    """
    print("=== 数据分布统计 ===")
    
    aca_dir = os.path.join('colon_subset', 'colon_aca')
    normal_dir = os.path.join('colon_subset', 'colon_n')
    
    # 统计文件数量
    aca_count = len([f for f in os.listdir(aca_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))])
    normal_count = len([f for f in os.listdir(normal_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))])
    
    total_count = aca_count + normal_count
    
    print(f"癌细胞图像: {aca_count} ({aca_count/total_count:.1%})")
    print(f"良性细胞图像: {normal_count} ({normal_count/total_count:.1%})")
    print(f"总图像数: {total_count}")
    
    # 绘制分布图
    labels = ['癌细胞', '良性细胞']
    sizes = [aca_count, normal_count]
    colors = ['#ff6b6b', '#4ecdc4']
    
    plt.figure(figsize=(10, 6))
    
    # 饼图
    plt.subplot(1, 2, 1)
    plt.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
    plt.title('数据分布')
    
    # 柱状图
    plt.subplot(1, 2, 2)
    bars = plt.bar(labels, sizes, color=colors)
    plt.title('样本数量')
    plt.ylabel('图像数量')
    
    # 在柱状图上添加数值
    for bar, size in zip(bars, sizes):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 10,
                str(size), ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig('data_distribution.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """
    主测试函数
    """
    print("结肠癌细胞分类器测试程序")
    print("=" * 50)
    
    while True:
        print("\n请选择测试选项:")
        print("1. 显示数据分布")
        print("2. 测试集完整评估")
        print("3. 测试集随机样本可视化")
        print("4. 测试特定图像")
        print("5. 退出")
        
        choice = input("\n请输入选项 (1-5): ").strip()
        
        if choice == '1':
            show_data_distribution()
        elif choice == '2':
            test_on_test_set()
        elif choice == '3':
            test_random_samples_from_test_set()
        elif choice == '4':
            image_path = input("请输入图像路径: ").strip()
            test_specific_image(image_path)
        elif choice == '5':
            print("退出测试程序")
            break
        else:
            print("无效选项，请重新选择")

if __name__ == "__main__":
    main()
