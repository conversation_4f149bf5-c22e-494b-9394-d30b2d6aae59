#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPU检测和诊断脚本
"""

import os
import sys

def check_nvidia_driver():
    """检查NVIDIA驱动"""
    print("=== 检查NVIDIA驱动 ===")
    try:
        import subprocess
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ NVIDIA驱动正常")
            print(result.stdout)
            return True
        else:
            print("❌ nvidia-smi命令失败")
            return False
    except FileNotFoundError:
        print("❌ 未找到nvidia-smi命令，请检查NVIDIA驱动安装")
        return False
    except Exception as e:
        print(f"❌ 检查NVIDIA驱动时出错: {e}")
        return False

def check_cuda():
    """检查CUDA"""
    print("\n=== 检查CUDA ===")
    try:
        import subprocess
        result = subprocess.run(['nvcc', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ CUDA编译器可用")
            print(result.stdout)
            return True
        else:
            print("❌ nvcc命令失败")
            return False
    except FileNotFoundError:
        print("❌ 未找到nvcc命令，CUDA可能未正确安装")
        return False
    except Exception as e:
        print(f"❌ 检查CUDA时出错: {e}")
        return False

def check_tensorflow():
    """检查TensorFlow GPU支持"""
    print("\n=== 检查TensorFlow ===")
    try:
        import tensorflow as tf
        print(f"TensorFlow版本: {tf.__version__}")
        
        # 检查CUDA支持
        print(f"TensorFlow是否支持CUDA: {tf.test.is_built_with_cuda()}")
        
        # 检查GPU设备
        gpus = tf.config.list_physical_devices('GPU')
        print(f"检测到的GPU设备数量: {len(gpus)}")
        
        if gpus:
            print("✅ TensorFlow检测到GPU设备:")
            for i, gpu in enumerate(gpus):
                print(f"   GPU {i}: {gpu.name}")
                
            # 测试GPU计算
            try:
                with tf.device('/GPU:0'):
                    a = tf.constant([[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]])
                    b = tf.constant([[1.0, 2.0], [3.0, 4.0], [5.0, 6.0]])
                    c = tf.matmul(a, b)
                    print(f"✅ GPU计算测试成功: {c.device}")
                    print(f"计算结果: {c.numpy()}")
                return True
            except Exception as e:
                print(f"❌ GPU计算测试失败: {e}")
                return False
        else:
            print("❌ TensorFlow未检测到GPU设备")
            return False
            
    except ImportError:
        print("❌ 未安装TensorFlow")
        return False
    except Exception as e:
        print(f"❌ 检查TensorFlow时出错: {e}")
        return False

def check_pytorch():
    """检查PyTorch GPU支持"""
    print("\n=== 检查PyTorch ===")
    try:
        import torch
        print(f"PyTorch版本: {torch.__version__}")
        print(f"CUDA是否可用: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            print(f"CUDA版本: {torch.version.cuda}")
            print(f"GPU设备数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                print(f"   GPU {i}: {torch.cuda.get_device_name(i)}")
            
            # 测试GPU计算
            try:
                device = torch.device('cuda:0')
                x = torch.randn(3, 3).to(device)
                y = torch.randn(3, 3).to(device)
                z = torch.matmul(x, y)
                print(f"✅ PyTorch GPU计算测试成功")
                print(f"设备: {z.device}")
                return True
            except Exception as e:
                print(f"❌ PyTorch GPU计算测试失败: {e}")
                return False
        else:
            print("❌ PyTorch未检测到CUDA")
            return False
            
    except ImportError:
        print("❌ 未安装PyTorch")
        return False
    except Exception as e:
        print(f"❌ 检查PyTorch时出错: {e}")
        return False

def suggest_solutions():
    """提供解决方案建议"""
    print("\n=== 解决方案建议 ===")
    print("如果TensorFlow无法使用GPU，请尝试以下解决方案:")
    print()
    print("1. 安装支持GPU的TensorFlow:")
    print("   pip uninstall tensorflow")
    print("   pip install tensorflow[and-cuda]")
    print()
    print("2. 或者安装特定版本:")
    print("   pip install tensorflow-gpu==2.12.0")
    print()
    print("3. 检查CUDA和cuDNN版本兼容性:")
    print("   TensorFlow 2.12: CUDA 11.8, cuDNN 8.6")
    print("   TensorFlow 2.13+: CUDA 11.8, cuDNN 8.7")
    print()
    print("4. 设置环境变量:")
    print("   set CUDA_VISIBLE_DEVICES=0")
    print()
    print("5. 重启Python环境后重试")

def main():
    """主函数"""
    print("🔍 GPU环境诊断工具")
    print("="*50)
    
    # 检查各个组件
    nvidia_ok = check_nvidia_driver()
    cuda_ok = check_cuda()
    tf_ok = check_tensorflow()
    pytorch_ok = check_pytorch()
    
    print("\n" + "="*50)
    print("📊 诊断结果汇总:")
    print(f"NVIDIA驱动: {'✅' if nvidia_ok else '❌'}")
    print(f"CUDA: {'✅' if cuda_ok else '❌'}")
    print(f"TensorFlow GPU: {'✅' if tf_ok else '❌'}")
    print(f"PyTorch GPU: {'✅' if pytorch_ok else '❌'}")
    
    if not tf_ok:
        suggest_solutions()

if __name__ == "__main__":
    main()
