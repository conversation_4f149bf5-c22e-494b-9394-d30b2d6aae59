#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
结肠癌细胞分类器
使用深度学习对结肠癌细胞图像进行分类
"""

import os
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.model_selection import train_test_split
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader, Dataset
from torchvision import transforms, models, datasets
from torchvision.transforms import functional as TF
import pickle
import cv2
from pathlib import Path
from PIL import Image
import time

# 配置GPU
def setup_gpu():
    """配置GPU设置"""
    print("检查GPU可用性...")
    print(f"PyTorch版本: {torch.__version__}")

    # 检查CUDA可用性
    print(f"PyTorch是否支持CUDA: {torch.cuda.is_available()}")

    if torch.cuda.is_available():
        try:
            # 显示GPU信息
            gpu_count = torch.cuda.device_count()
            print(f"✅ 找到 {gpu_count} 个GPU设备:")
            for i in range(gpu_count):
                gpu_name = torch.cuda.get_device_name(i)
                print(f"   GPU {i}: {gpu_name}")

            # 显示CUDA版本
            print(f"CUDA版本: {torch.version.cuda}")

            # 测试GPU是否真正可用
            try:
                device = torch.device('cuda:0')
                test_tensor = torch.randn(2, 2).to(device)
                result = torch.matmul(test_tensor, test_tensor)
                print(f"✅ GPU计算测试成功，设备: {result.device}")

                # 显示GPU内存信息
                memory_allocated = torch.cuda.memory_allocated(0) / 1024**2  # MB
                memory_cached = torch.cuda.memory_reserved(0) / 1024**2  # MB
                print(f"GPU内存使用: {memory_allocated:.1f}MB (已分配), {memory_cached:.1f}MB (已缓存)")

                return True

            except Exception as e:
                print(f"❌ GPU计算测试失败: {e}")
                return False

        except RuntimeError as e:
            print(f"❌ GPU配置失败: {e}")
            print("⚠️ 将回退到CPU训练")
            return False
    else:
        print("⚠️ 未找到GPU设备，将使用CPU训练")
        print("\n可能的解决方案:")
        print("1. 安装支持GPU的PyTorch版本:")
        print("   pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121")
        print("2. 确保CUDA版本兼容")
        print("3. 检查NVIDIA驱动是否最新")
        return False

class ColonCancerModel(nn.Module):
    """PyTorch结肠癌分类模型"""

    def __init__(self, num_classes=1):
        super(ColonCancerModel, self).__init__()
        # 使用预训练的VGG16作为基础模型
        self.backbone = models.vgg16(pretrained=True)

        # 冻结特征提取层
        for param in self.backbone.features.parameters():
            param.requires_grad = False

        # 替换分类器
        self.backbone.classifier = nn.Sequential(
            nn.AdaptiveAvgPool2d((7, 7)),
            nn.Flatten(),
            nn.Linear(512 * 7 * 7, 4096),
            nn.ReLU(True),
            nn.Dropout(0.5),
            nn.Linear(4096, 128),
            nn.ReLU(True),
            nn.Dropout(0.3),
            nn.Linear(128, 64),
            nn.ReLU(True),
            nn.Dropout(0.2),
            nn.Linear(64, num_classes)
        )

    def forward(self, x):
        return self.backbone(x)

class ColonCancerClassifier:
    """结肠癌细胞分类器类"""

    def __init__(self, img_size=(224, 224), batch_size=32):
        """
        初始化分类器

        Args:
            img_size: 图像尺寸
            batch_size: 批次大小
        """
        self.img_size = img_size
        self.batch_size = batch_size
        self.model = None
        self.history = None
        self.class_names = ['colon_aca', 'colon_n']  # 癌细胞, 正常细胞
        self.gpu_available = setup_gpu()  # 初始化时设置GPU
        self.device = torch.device('cuda:0' if self.gpu_available else 'cpu')

    def create_model(self):
        """创建CNN模型"""
        # 创建模型
        model = ColonCancerModel(num_classes=1)
        model = model.to(self.device)

        # 设置优化器和损失函数
        if self.gpu_available:
            # GPU训练时使用更大的学习率
            self.optimizer = optim.Adam(model.parameters(), lr=0.001)
        else:
            # CPU训练时使用较小的学习率
            self.optimizer = optim.Adam(model.parameters(), lr=0.0001)

        self.criterion = nn.BCEWithLogitsLoss()
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='min', factor=0.5, patience=5, min_lr=1e-7
        )

        self.model = model
        return model
    
    def prepare_data(self, data_dir='colon_dataset_split'):
        """
        准备训练和验证数据

        Args:
            data_dir: 数据集目录
        """
        train_dir = os.path.join(data_dir, 'train')
        test_dir = os.path.join(data_dir, 'test')

        # 数据增强 - GPU训练时可以使用更强的数据增强
        if self.gpu_available:
            # GPU训练时使用更强的数据增强
            train_transform = transforms.Compose([
                transforms.Resize(self.img_size),
                transforms.RandomRotation(30),
                transforms.RandomHorizontalFlip(),
                transforms.RandomVerticalFlip(),
                transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
                transforms.RandomAffine(degrees=0, translate=(0.3, 0.3), scale=(0.7, 1.3), shear=30),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])
        else:
            # CPU训练时使用较轻的数据增强
            train_transform = transforms.Compose([
                transforms.Resize(self.img_size),
                transforms.RandomRotation(20),
                transforms.RandomHorizontalFlip(),
                transforms.RandomVerticalFlip(),
                transforms.ColorJitter(brightness=0.1, contrast=0.1),
                transforms.RandomAffine(degrees=0, translate=(0.2, 0.2), scale=(0.8, 1.2), shear=20),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])

        # 验证和测试数据变换
        val_test_transform = transforms.Compose([
            transforms.Resize(self.img_size),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])

        # 创建数据集
        full_train_dataset = datasets.ImageFolder(train_dir, transform=train_transform)
        test_dataset = datasets.ImageFolder(test_dir, transform=val_test_transform)

        # 划分训练和验证集
        train_size = int(0.8 * len(full_train_dataset))
        val_size = len(full_train_dataset) - train_size
        train_dataset, val_dataset = torch.utils.data.random_split(
            full_train_dataset, [train_size, val_size]
        )

        # 为验证集设置不同的变换
        val_dataset.dataset = datasets.ImageFolder(train_dir, transform=val_test_transform)

        # 创建数据加载器
        train_loader = DataLoader(
            train_dataset,
            batch_size=self.batch_size,
            shuffle=True,
            num_workers=4 if self.gpu_available else 2
        )

        val_loader = DataLoader(
            val_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=4 if self.gpu_available else 2
        )

        test_loader = DataLoader(
            test_dataset,
            batch_size=self.batch_size,
            shuffle=False,
            num_workers=4 if self.gpu_available else 2
        )

        return train_loader, val_loader, test_loader
    
    def train(self, epochs=50, data_dir='colon_dataset_split'):
        """
        训练模型

        Args:
            epochs: 训练轮数
            data_dir: 数据集目录
        """
        print("开始准备数据...")
        train_loader, val_loader, test_loader = self.prepare_data(data_dir)

        print("创建模型...")
        self.create_model()

        print("模型结构:")
        print(self.model)

        # 训练历史记录
        self.history = {
            'train_loss': [],
            'train_acc': [],
            'val_loss': [],
            'val_acc': []
        }

        print("开始训练...")
        print(f"✅ 使用设备: {self.device}")
        print(f"训练批次大小: {self.batch_size}")

        best_val_acc = 0.0
        patience_counter = 0
        patience = 10

        for epoch in range(epochs):
            start_time = time.time()

            # 训练阶段
            self.model.train()
            train_loss = 0.0
            train_correct = 0
            train_total = 0

            for batch_idx, (data, target) in enumerate(train_loader):
                data, target = data.to(self.device), target.float().to(self.device)

                self.optimizer.zero_grad()
                output = self.model(data).squeeze()
                loss = self.criterion(output, target)
                loss.backward()
                self.optimizer.step()

                train_loss += loss.item()
                predicted = torch.sigmoid(output) > 0.5
                train_total += target.size(0)
                train_correct += (predicted == target).sum().item()

                if batch_idx % 10 == 0:
                    print(f'Epoch {epoch+1}/{epochs}, Batch {batch_idx}/{len(train_loader)}, '
                          f'Loss: {loss.item():.4f}')

            # 验证阶段
            self.model.eval()
            val_loss = 0.0
            val_correct = 0
            val_total = 0

            with torch.no_grad():
                for data, target in val_loader:
                    data, target = data.to(self.device), target.float().to(self.device)
                    output = self.model(data).squeeze()
                    loss = self.criterion(output, target)

                    val_loss += loss.item()
                    predicted = torch.sigmoid(output) > 0.5
                    val_total += target.size(0)
                    val_correct += (predicted == target).sum().item()

            # 计算平均指标
            train_loss /= len(train_loader)
            train_acc = train_correct / train_total
            val_loss /= len(val_loader)
            val_acc = val_correct / val_total

            # 记录历史
            self.history['train_loss'].append(train_loss)
            self.history['train_acc'].append(train_acc)
            self.history['val_loss'].append(val_loss)
            self.history['val_acc'].append(val_acc)

            # 学习率调度
            self.scheduler.step(val_loss)

            epoch_time = time.time() - start_time
            print(f'Epoch {epoch+1}/{epochs} - {epoch_time:.2f}s - '
                  f'train_loss: {train_loss:.4f} - train_acc: {train_acc:.4f} - '
                  f'val_loss: {val_loss:.4f} - val_acc: {val_acc:.4f}')

            # 早停和模型保存
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                patience_counter = 0
                torch.save(self.model.state_dict(), 'best_colon_cancer_model.pth')
                print(f'✅ 保存最佳模型 (val_acc: {val_acc:.4f})')
            else:
                patience_counter += 1
                if patience_counter >= patience:
                    print(f'早停：验证准确率在 {patience} 个epoch内没有改善')
                    break

        # 加载最佳模型
        self.model.load_state_dict(torch.load('best_colon_cancer_model.pth'))

        # 保存最终模型
        torch.save(self.model.state_dict(), 'colon_cancer_classifier.pth')
        print("模型已保存为 colon_cancer_classifier.pth")

        # 保存训练历史
        with open('training_history.pkl', 'wb') as f:
            pickle.dump(self.history, f)

        # 在测试集上评估
        print("\n在测试集上评估模型...")
        test_loss, test_acc = self.evaluate(test_loader)
        print(f"测试集损失: {test_loss:.4f}")
        print(f"测试集准确率: {test_acc:.4f}")

        # 绘制训练历史
        self.plot_training_history()

        return self.history

    def evaluate(self, test_loader):
        """评估模型"""
        self.model.eval()
        test_loss = 0.0
        test_correct = 0
        test_total = 0

        with torch.no_grad():
            for data, target in test_loader:
                data, target = data.to(self.device), target.float().to(self.device)
                output = self.model(data).squeeze()
                loss = self.criterion(output, target)

                test_loss += loss.item()
                predicted = torch.sigmoid(output) > 0.5
                test_total += target.size(0)
                test_correct += (predicted == target).sum().item()

        test_loss /= len(test_loader)
        test_acc = test_correct / test_total
        return test_loss, test_acc

    def plot_training_history(self):
        """绘制训练历史"""
        if self.history is None:
            print("没有训练历史可绘制")
            return

        plt.figure(figsize=(12, 4))

        # 绘制准确率
        plt.subplot(1, 2, 1)
        plt.plot(self.history['train_acc'], label='训练准确率')
        plt.plot(self.history['val_acc'], label='验证准确率')
        plt.title('模型准确率')
        plt.xlabel('轮次')
        plt.ylabel('准确率')
        plt.legend()
        plt.grid(True)

        # 绘制损失
        plt.subplot(1, 2, 2)
        plt.plot(self.history['train_loss'], label='训练损失')
        plt.plot(self.history['val_loss'], label='验证损失')
        plt.title('模型损失')
        plt.xlabel('轮次')
        plt.ylabel('损失')
        plt.legend()
        plt.grid(True)

        plt.tight_layout()
        plt.savefig('training_history.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("训练历史图已保存为 training_history.png")
    
    def load_model(self, model_path):
        """
        加载已训练的模型

        Args:
            model_path: 模型文件路径
        """
        if os.path.exists(model_path):
            if self.model is None:
                self.create_model()
            self.model.load_state_dict(torch.load(model_path, map_location=self.device))
            self.model.eval()
            print(f"模型已从 {model_path} 加载")
            return True
        else:
            print(f"模型文件 {model_path} 不存在")
            return False

    def predict_image(self, image_path):
        """
        预测单张图像

        Args:
            image_path: 图像文件路径
        """
        if self.model is None:
            print("请先加载模型")
            return None

        # 读取和预处理图像
        img = Image.open(image_path).convert('RGB')

        # 应用变换
        transform = transforms.Compose([
            transforms.Resize(self.img_size),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])

        img_tensor = transform(img).unsqueeze(0).to(self.device)

        # 预测
        self.model.eval()
        with torch.no_grad():
            output = self.model(img_tensor).squeeze()
            prediction = torch.sigmoid(output).item()

        # 解释结果
        if prediction > 0.5:
            result = "正常细胞"
            confidence = prediction
        else:
            result = "癌细胞"
            confidence = 1 - prediction

        return {
            'prediction': result,
            'confidence': confidence,
            'raw_score': prediction
        }
    
    def load_data_split(self, split_file='data_split.pkl'):
        """加载数据划分信息"""
        if os.path.exists(split_file):
            with open(split_file, 'rb') as f:
                self.data_split = pickle.load(f)
            return True
        return False

def main():
    """主函数"""
    print("🔬 结肠癌细胞分类器训练程序")
    print("="*50)

    # 显示TensorFlow和GPU信息
    print(f"TensorFlow版本: {tf.__version__}")

    # 检查数据集是否存在
    data_dir = 'colon_dataset_split'
    if not os.path.exists(data_dir):
        print(f"❌ 数据集目录不存在: {data_dir}")
        print("请先运行数据划分脚本")
        return

    # 创建分类器实例（会自动设置GPU）
    classifier = ColonCancerClassifier()

    # 显示训练设备信息
    if classifier.gpu_available:
        print("🚀 将使用GPU加速训练")
        # 根据GPU调整训练参数
        epochs = 50  # GPU训练可以使用更多轮次
        print(f"训练轮次: {epochs}")
    else:
        print("💻 将使用CPU训练")
        epochs = 30  # CPU训练使用较少轮次
        print(f"训练轮次: {epochs}")

    # 开始训练
    try:
        classifier.train(epochs=epochs)
        print("\n✅ 训练完成!")
    except KeyboardInterrupt:
        print("\n⚠️ 训练被用户中断")
    except Exception as e:
        print(f"\n❌ 训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
