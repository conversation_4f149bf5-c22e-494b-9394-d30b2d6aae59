#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
结肠癌细胞分类器
使用深度学习对结肠癌细胞图像进行分类
"""

import os
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.model_selection import train_test_split
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from tensorflow.keras.applications import VGG16
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import ModelCheckpoint, EarlyStopping, ReduceLROnPlateau
import pickle
import cv2
from pathlib import Path

class ColonCancerClassifier:
    """结肠癌细胞分类器类"""
    
    def __init__(self, img_size=(224, 224), batch_size=32):
        """
        初始化分类器
        
        Args:
            img_size: 图像尺寸
            batch_size: 批次大小
        """
        self.img_size = img_size
        self.batch_size = batch_size
        self.model = None
        self.history = None
        self.class_names = ['colon_aca', 'colon_n']  # 癌细胞, 正常细胞
        
    def create_model(self):
        """创建CNN模型"""
        # 使用预训练的VGG16作为基础模型
        base_model = VGG16(
            weights='imagenet',
            include_top=False,
            input_shape=(*self.img_size, 3)
        )
        
        # 冻结基础模型的权重
        base_model.trainable = False
        
        # 构建完整模型
        model = keras.Sequential([
            base_model,
            layers.GlobalAveragePooling2D(),
            layers.Dropout(0.5),
            layers.Dense(128, activation='relu'),
            layers.Dropout(0.3),
            layers.Dense(64, activation='relu'),
            layers.Dropout(0.2),
            layers.Dense(1, activation='sigmoid')  # 二分类
        ])
        
        # 编译模型
        model.compile(
            optimizer=Adam(learning_rate=0.0001),
            loss='binary_crossentropy',
            metrics=['accuracy']
        )
        
        self.model = model
        return model
    
    def prepare_data(self, data_dir='colon_dataset_split'):
        """
        准备训练和验证数据
        
        Args:
            data_dir: 数据集目录
        """
        train_dir = os.path.join(data_dir, 'train')
        test_dir = os.path.join(data_dir, 'test')
        
        # 数据增强
        train_datagen = ImageDataGenerator(
            rescale=1./255,
            rotation_range=20,
            width_shift_range=0.2,
            height_shift_range=0.2,
            horizontal_flip=True,
            vertical_flip=True,
            zoom_range=0.2,
            shear_range=0.2,
            fill_mode='nearest',
            validation_split=0.2  # 从训练集中分出20%作为验证集
        )
        
        test_datagen = ImageDataGenerator(rescale=1./255)
        
        # 创建数据生成器
        train_generator = train_datagen.flow_from_directory(
            train_dir,
            target_size=self.img_size,
            batch_size=self.batch_size,
            class_mode='binary',
            subset='training',
            shuffle=True
        )
        
        validation_generator = train_datagen.flow_from_directory(
            train_dir,
            target_size=self.img_size,
            batch_size=self.batch_size,
            class_mode='binary',
            subset='validation',
            shuffle=True
        )
        
        test_generator = test_datagen.flow_from_directory(
            test_dir,
            target_size=self.img_size,
            batch_size=self.batch_size,
            class_mode='binary',
            shuffle=False
        )
        
        return train_generator, validation_generator, test_generator
    
    def train(self, epochs=50, data_dir='colon_dataset_split'):
        """
        训练模型
        
        Args:
            epochs: 训练轮数
            data_dir: 数据集目录
        """
        print("开始准备数据...")
        train_gen, val_gen, test_gen = self.prepare_data(data_dir)
        
        print("创建模型...")
        self.create_model()
        
        print("模型结构:")
        self.model.summary()
        
        # 设置回调函数
        callbacks = [
            ModelCheckpoint(
                'best_colon_cancer_model.h5',
                monitor='val_accuracy',
                save_best_only=True,
                mode='max',
                verbose=1
            ),
            EarlyStopping(
                monitor='val_loss',
                patience=10,
                restore_best_weights=True,
                verbose=1
            ),
            ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=5,
                min_lr=1e-7,
                verbose=1
            )
        ]
        
        print("开始训练...")
        # 训练模型
        self.history = self.model.fit(
            train_gen,
            epochs=epochs,
            validation_data=val_gen,
            callbacks=callbacks,
            verbose=1
        )
        
        # 保存最终模型
        self.model.save('colon_cancer_classifier.h5')
        print("模型已保存为 colon_cancer_classifier.h5")
        
        # 保存训练历史
        with open('training_history.pkl', 'wb') as f:
            pickle.dump(self.history.history, f)
        
        # 在测试集上评估
        print("\n在测试集上评估模型...")
        test_loss, test_accuracy = self.model.evaluate(test_gen, verbose=1)
        print(f"测试集准确率: {test_accuracy:.4f}")
        
        # 绘制训练历史
        self.plot_training_history()
        
        return self.history
    
    def plot_training_history(self):
        """绘制训练历史"""
        if self.history is None:
            print("没有训练历史可绘制")
            return
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
        
        # 绘制准确率
        ax1.plot(self.history.history['accuracy'], label='训练准确率')
        ax1.plot(self.history.history['val_accuracy'], label='验证准确率')
        ax1.set_title('模型准确率')
        ax1.set_xlabel('轮次')
        ax1.set_ylabel('准确率')
        ax1.legend()
        ax1.grid(True)
        
        # 绘制损失
        ax2.plot(self.history.history['loss'], label='训练损失')
        ax2.plot(self.history.history['val_loss'], label='验证损失')
        ax2.set_title('模型损失')
        ax2.set_xlabel('轮次')
        ax2.set_ylabel('损失')
        ax2.legend()
        ax2.grid(True)
        
        plt.tight_layout()
        plt.savefig('training_history.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("训练历史图已保存为 training_history.png")
    
    def load_model(self, model_path):
        """
        加载已训练的模型
        
        Args:
            model_path: 模型文件路径
        """
        if os.path.exists(model_path):
            self.model = keras.models.load_model(model_path)
            print(f"模型已从 {model_path} 加载")
            return True
        else:
            print(f"模型文件 {model_path} 不存在")
            return False
    
    def predict_image(self, image_path):
        """
        预测单张图像
        
        Args:
            image_path: 图像文件路径
        """
        if self.model is None:
            print("请先加载模型")
            return None
        
        # 读取和预处理图像
        img = cv2.imread(image_path)
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        img = cv2.resize(img, self.img_size)
        img = img.astype('float32') / 255.0
        img = np.expand_dims(img, axis=0)
        
        # 预测
        prediction = self.model.predict(img)[0][0]
        
        # 解释结果
        if prediction > 0.5:
            result = "正常细胞"
            confidence = prediction
        else:
            result = "癌细胞"
            confidence = 1 - prediction
        
        return {
            'prediction': result,
            'confidence': confidence,
            'raw_score': prediction
        }
    
    def load_data_split(self, split_file='data_split.pkl'):
        """加载数据划分信息"""
        if os.path.exists(split_file):
            with open(split_file, 'rb') as f:
                self.data_split = pickle.load(f)
            return True
        return False

def main():
    """主函数"""
    print("🔬 结肠癌细胞分类器训练程序")
    print("="*50)
    
    # 检查数据集是否存在
    data_dir = 'colon_dataset_split'
    if not os.path.exists(data_dir):
        print(f"❌ 数据集目录不存在: {data_dir}")
        print("请先运行数据划分脚本")
        return
    
    # 创建分类器实例
    classifier = ColonCancerClassifier()
    
    # 开始训练
    try:
        classifier.train(epochs=30)
        print("\n✅ 训练完成!")
    except KeyboardInterrupt:
        print("\n⚠️ 训练被用户中断")
    except Exception as e:
        print(f"\n❌ 训练过程中出现错误: {e}")

if __name__ == "__main__":
    main()
