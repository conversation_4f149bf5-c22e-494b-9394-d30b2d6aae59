#!/usr/bin/env python3
"""
结肠癌细胞分类器 - 快速启动脚本
"""

import os
import sys
import subprocess
from pathlib import Path

def check_dependencies():
    """跳过依赖包检查"""
    print("⏭️ 跳过依赖包检查")
    return True

def check_data():
    """检查数据集是否存在"""
    data_dir = 'colon_subset'
    aca_dir = os.path.join(data_dir, 'colon_aca')
    normal_dir = os.path.join(data_dir, 'colon_n')

    if not os.path.exists(data_dir):
        print(f"❌ 数据集目录不存在: {data_dir}")
        return False

    if not os.path.exists(aca_dir):
        print(f"❌ 癌细胞数据目录不存在: {aca_dir}")
        return False

    if not os.path.exists(normal_dir):
        print(f"❌ 良性细胞数据目录不存在: {normal_dir}")
        return False

    # 统计文件数量
    aca_files = [f for f in os.listdir(aca_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
    normal_files = [f for f in os.listdir(normal_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]

    print(f"✅ 数据集检查通过:")
    print(f"   - 癌细胞图像: {len(aca_files)} 张")
    print(f"   - 良性细胞图像: {len(normal_files)} 张")
    print(f"   - 总计: {len(aca_files) + len(normal_files)} 张")

    return True

def check_split_data():
    """检查是否已经划分了训练集和测试集"""
    split_dir = 'colon_dataset_split'
    train_dir = os.path.join(split_dir, 'train')
    test_dir = os.path.join(split_dir, 'test')

    if not os.path.exists(split_dir):
        return False

    # 检查训练集和测试集目录结构
    required_dirs = [
        os.path.join(train_dir, 'colon_aca'),
        os.path.join(train_dir, 'colon_n'),
        os.path.join(test_dir, 'colon_aca'),
        os.path.join(test_dir, 'colon_n')
    ]

    for dir_path in required_dirs:
        if not os.path.exists(dir_path):
            return False

    # 统计文件数量
    train_aca = len([f for f in os.listdir(os.path.join(train_dir, 'colon_aca')) if f.lower().endswith(('.jpg', '.jpeg', '.png'))])
    train_normal = len([f for f in os.listdir(os.path.join(train_dir, 'colon_n')) if f.lower().endswith(('.jpg', '.jpeg', '.png'))])
    test_aca = len([f for f in os.listdir(os.path.join(test_dir, 'colon_aca')) if f.lower().endswith(('.jpg', '.jpeg', '.png'))])
    test_normal = len([f for f in os.listdir(os.path.join(test_dir, 'colon_n')) if f.lower().endswith(('.jpg', '.jpeg', '.png'))])

    if train_aca > 0 and train_normal > 0 and test_aca > 0 and test_normal > 0:
        print(f"✅ 已划分的数据集:")
        print(f"   训练集 - 癌细胞: {train_aca} 张, 良性: {train_normal} 张")
        print(f"   测试集 - 癌细胞: {test_aca} 张, 良性: {test_normal} 张")
        return True

    return False

def check_model():
    """检查模型文件是否存在"""
    model_files = ['colon_cancer_classifier.h5', 'best_colon_cancer_model.h5']
    
    for model_file in model_files:
        if os.path.exists(model_file):
            print(f"✅ 找到模型文件: {model_file}")
            return True
    
    print("❌ 未找到训练好的模型文件")
    print("   需要先训练模型")
    return False

def run_training():
    """运行训练脚本"""
    print("\n" + "="*50)
    print("开始训练模型...")
    print("="*50)
    
    try:
        subprocess.run([sys.executable, 'colon_cancer_classifier.py'], check=True)
        print("\n✅ 模型训练完成!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"\n❌ 训练失败: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⚠️ 训练被用户中断")
        return False

def run_ui():
    """运行图形界面"""
    print("\n" + "="*50)
    print("启动图形界面...")
    print("="*50)
    
    try:
        subprocess.run([sys.executable, 'colon_cancer_detector_ui.py'], check=True)
    except subprocess.CalledProcessError as e:
        print(f"\n❌ 界面启动失败: {e}")
    except KeyboardInterrupt:
        print("\n⚠️ 界面被用户关闭")

def run_data_split():
    """运行数据划分脚本"""
    print("\n" + "="*50)
    print("开始划分数据集...")
    print("="*50)

    try:
        subprocess.run([sys.executable, '数据划分.py'], check=True)
        print("\n✅ 数据集划分完成!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"\n❌ 数据划分失败: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⚠️ 数据划分被用户中断")
        return False

def run_test():
    """运行测试脚本"""
    print("\n" + "="*50)
    print("启动测试程序...")
    print("="*50)

    try:
        subprocess.run([sys.executable, 'test_classifier.py'], check=True)
    except subprocess.CalledProcessError as e:
        print(f"\n❌ 测试失败: {e}")
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")

def main():
    """主函数"""
    print("🔬 结肠癌细胞分类器 - 快速启动")
    print("="*50)

    # 检查依赖
    if not check_dependencies():
        return

    # 检查数据
    if not check_data():
        print("\n请确保数据集目录结构正确:")
        print("colon_subset/")
        print("├── colon_aca/    # 癌细胞图像")
        print("└── colon_n/      # 良性细胞图像")
        return

    # 检查是否已划分数据集
    data_split_exists = check_split_data()

    # 检查模型
    model_exists = check_model()

    print("\n" + "="*50)
    print("请选择操作:")
    print("1. 划分数据集 (80%训练集, 20%测试集)")
    print("2. 训练模型 (需要先划分数据集)")
    print("3. 启动图形界面 (需要已训练的模型)")
    print("4. 运行测试程序 (需要已训练的模型)")
    print("5. 退出")
    print("="*50)
    
    while True:
        try:
            choice = input("\n请输入选项 (1-5): ").strip()

            if choice == '1':
                if run_data_split():
                    print("\n数据集划分完成! 现在可以训练模型")
                    data_split_exists = True
                break

            elif choice == '2':
                if not data_split_exists:
                    print("❌ 未找到划分的数据集，请先划分数据集 (选项1)")
                    continue
                if run_training():
                    print("\n训练完成! 现在可以使用图形界面或测试程序")
                break

            elif choice == '3':
                if not model_exists:
                    print("❌ 未找到模型文件，请先训练模型 (选项2)")
                    continue
                run_ui()
                break

            elif choice == '4':
                if not model_exists:
                    print("❌ 未找到模型文件，请先训练模型 (选项2)")
                    continue
                run_test()
                break

            elif choice == '5':
                print("退出程序")
                break

            else:
                print("❌ 无效选项，请输入 1-5")

        except KeyboardInterrupt:
            print("\n\n退出程序")
            break
        except EOFError:
            print("\n\n退出程序")
            break

if __name__ == "__main__":
    main()
