#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检测TensorFlow相关包的安装情况
"""

import subprocess
import sys
import pkg_resources

def check_installed_packages():
    """检查已安装的TensorFlow相关包"""
    print("🔍 检查已安装的TensorFlow相关包")
    print("="*50)
    
    # 获取所有已安装的包
    try:
        result = subprocess.run([sys.executable, '-m', 'pip', 'list'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            tf_packages = []
            
            for line in lines:
                if 'tensorflow' in line.lower():
                    tf_packages.append(line.strip())
            
            if tf_packages:
                print("找到以下TensorFlow相关包:")
                for pkg in tf_packages:
                    print(f"  📦 {pkg}")
            else:
                print("❌ 未找到任何TensorFlow相关包")
                
            return tf_packages
        else:
            print(f"❌ 获取包列表失败: {result.stderr}")
            return []
            
    except Exception as e:
        print(f"❌ 检查包时出错: {e}")
        return []

def check_tensorflow_imports():
    """检查TensorFlow导入情况"""
    print("\n🔍 检查TensorFlow导入情况")
    print("="*30)
    
    try:
        import tensorflow as tf
        print(f"✅ TensorFlow导入成功")
        print(f"版本: {tf.__version__}")
        print(f"安装路径: {tf.__file__}")
        
        # 检查CUDA支持
        print(f"CUDA支持: {tf.test.is_built_with_cuda()}")
        
        # 检查GPU设备
        gpus = tf.config.list_physical_devices('GPU')
        print(f"GPU设备数量: {len(gpus)}")
        
        if gpus:
            for i, gpu in enumerate(gpus):
                print(f"  GPU {i}: {gpu.name}")
        
        return True
        
    except ImportError as e:
        print(f"❌ TensorFlow导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 检查TensorFlow时出错: {e}")
        return False

def check_cuda_libraries():
    """检查CUDA相关库"""
    print("\n🔍 检查CUDA相关库")
    print("="*25)
    
    cuda_libs = [
        'nvidia-cublas-cu12',
        'nvidia-cuda-cupti-cu12', 
        'nvidia-cuda-nvrtc-cu12',
        'nvidia-cuda-runtime-cu12',
        'nvidia-cudnn-cu12',
        'nvidia-cufft-cu12',
        'nvidia-curand-cu12',
        'nvidia-cusolver-cu12',
        'nvidia-cusparse-cu12',
        'nvidia-nccl-cu12',
        'nvidia-nvjitlink-cu12'
    ]
    
    try:
        result = subprocess.run([sys.executable, '-m', 'pip', 'list'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            installed_lines = result.stdout.lower()
            
            for lib in cuda_libs:
                if lib in installed_lines:
                    print(f"✅ {lib}")
                else:
                    print(f"❌ {lib}")
                    
    except Exception as e:
        print(f"❌ 检查CUDA库时出错: {e}")

def suggest_cleanup():
    """建议清理方案"""
    print("\n💡 建议的清理和重新安装方案")
    print("="*40)
    
    print("1. 完全卸载所有TensorFlow相关包:")
    print("   pip uninstall tensorflow tensorflow-gpu tensorflow-cpu -y")
    print("   pip uninstall tf-nightly tf-nightly-gpu -y")
    print()
    
    print("2. 清理CUDA相关包 (可选):")
    print("   pip uninstall nvidia-* -y")
    print()
    
    print("3. 重新安装支持GPU的TensorFlow:")
    print("   pip install tensorflow[and-cuda]==2.15.0")
    print("   或")
    print("   pip install tensorflow-gpu==2.12.0")
    print()
    
    print("4. 验证安装:")
    print("   python -c \"import tensorflow as tf; print(tf.config.list_physical_devices('GPU'))\"")
    print()
    
    print("5. 如果仍有问题，尝试conda安装:")
    print("   conda install tensorflow-gpu")

def main():
    """主函数"""
    print("🔧 TensorFlow包检测和诊断工具")
    print("="*50)
    
    # 检查已安装的包
    tf_packages = check_installed_packages()
    
    # 检查导入情况
    import_ok = check_tensorflow_imports()
    
    # 检查CUDA库
    check_cuda_libraries()
    
    # 分析结果
    print("\n📊 诊断结果:")
    print("="*20)
    
    if len(tf_packages) > 1:
        print("⚠️ 检测到多个TensorFlow相关包，可能存在冲突")
    elif len(tf_packages) == 0:
        print("❌ 未安装TensorFlow")
    else:
        print("✅ 只安装了一个TensorFlow包")
    
    if not import_ok:
        print("❌ TensorFlow导入失败")
    
    # 提供建议
    if len(tf_packages) > 1 or not import_ok:
        suggest_cleanup()

if __name__ == "__main__":
    main()
